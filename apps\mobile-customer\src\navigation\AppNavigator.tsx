import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import OrdersScreen from '../screens/OrdersScreen';
import CartScreen from '../screens/CartScreen';
import SearchScreen from '../screens/SearchScreen';
import AccountScreen from '../screens/AccountScreen';
import WishlistScreen from '../screens/WishlistScreen';
import NotificationsScreen from '../screens/NotificationsScreen';

// Safe import with error handling for Expo Go compatibility
let createStackNavigator: any = null;
let Stack: any = null;

try {
  const stackModule = require('@react-navigation/stack');
  createStackNavigator = stackModule.createStackNavigator;

  if (typeof createStackNavigator === 'function') {
    Stack = createStackNavigator();
  } else {
    console.warn('createStackNavigator is not a function:', typeof createStackNavigator);
  }
} catch (error) {
  console.error('Failed to import @react-navigation/stack:', error);
}

// Fallback component for when Stack Navigator is not available
function FallbackNavigator() {
  return (
    <View style={styles.fallbackContainer}>
      <Text style={styles.fallbackTitle}>Navigation Error</Text>
      <Text style={styles.fallbackMessage}>
        React Navigation Stack is not available in Expo Go.
      </Text>
      <Text style={styles.fallbackSolution}>
        Please use Development Build or check your dependencies.
      </Text>
      <View style={styles.fallbackContent}>
        <HomeScreen />
      </View>
    </View>
  );
}

// Main App Navigator (Customer App) - SPA-STYLE NAVIGATION
export default function AppNavigator() {
  // If Stack Navigator is not available, show fallback
  if (!Stack || !createStackNavigator) {
    console.warn('Stack Navigator not available, showing fallback');
    return <FallbackNavigator />;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyleInterpolator: () => ({
          cardStyle: {
            opacity: 1,
            transform: [],
          },
        }),
        gestureEnabled: false,
        animation: 'none',
        transitionSpec: {
          open: {
            animation: 'timing',
            config: {
              duration: 0,
            },
          },
          close: {
            animation: 'timing',
            config: {
              duration: 0,
            },
          },
        },
      }}
    >
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="Orders" component={OrdersScreen} />
      <Stack.Screen name="Cart" component={CartScreen} />
      <Stack.Screen name="Search" component={SearchScreen} />
      <Stack.Screen name="Account" component={AccountScreen} />
      <Stack.Screen name="Wishlist" component={WishlistScreen} />
      <Stack.Screen name="Notifications" component={NotificationsScreen} />
    </Stack.Navigator>
  );
}

const styles = StyleSheet.create({
  fallbackContainer: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  fallbackTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 10,
    textAlign: 'center',
  },
  fallbackMessage: {
    fontSize: 16,
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  fallbackSolution: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  fallbackContent: {
    flex: 1,
  },
});
