import React, { useState, createContext, useContext } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import OrdersScreen from '../screens/OrdersScreen';
import CartScreen from '../screens/CartScreen';
import SearchScreen from '../screens/SearchScreen';
import AccountScreen from '../screens/AccountScreen';
import WishlistScreen from '../screens/WishlistScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import FooterNavigation from '../components/FooterNavigation';

// Navigation Context for fallback navigator
interface NavigationContextType {
  currentScreen: string;
  navigate: (screenName: string) => void;
  goBack: () => void;
}

const NavigationContext = createContext<NavigationContextType | null>(null);

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within NavigationProvider');
  }
  return context;
};

// Safe import with error handling for Expo Go compatibility
let createStackNavigator: any = null;
let Stack: any = null;

try {
  // Try different import methods for better compatibility
  let stackModule;
  try {
    stackModule = require('@react-navigation/stack');
  } catch (requireError) {
    console.warn('require failed, trying dynamic import:', requireError);
    // Fallback - we'll handle this in the component
  }

  if (stackModule && stackModule.createStackNavigator) {
    createStackNavigator = stackModule.createStackNavigator;

    if (typeof createStackNavigator === 'function') {
      Stack = createStackNavigator();
      console.log('✅ Stack Navigator initialized successfully');
    } else {
      console.warn('createStackNavigator is not a function:', typeof createStackNavigator);
    }
  } else {
    console.warn('createStackNavigator not found in module:', stackModule);
  }
} catch (error) {
  console.error('Failed to import @react-navigation/stack:', error);
}

// Fallback component for when Stack Navigator is not available
function FallbackNavigator() {
  const [currentScreen, setCurrentScreen] = useState('Home');

  const navigate = (screenName: string) => {
    console.log(`Navigating to: ${screenName}`);
    setCurrentScreen(screenName);
  };

  const goBack = () => {
    console.log('Going back to Home screen');
    setCurrentScreen('Home');
  };

  const navigationValue = {
    currentScreen,
    navigate,
    goBack,
  };

  const renderScreen = () => {
    const mockNavigation = { navigate, goBack };

    switch (currentScreen) {
      case 'Home':
        return <HomeScreen navigation={mockNavigation} />;
      case 'Orders':
        return <OrdersScreen navigation={mockNavigation} />;
      case 'Cart':
        return <CartScreen navigation={mockNavigation} />;
      case 'Search':
        return <SearchScreen navigation={mockNavigation} />;
      case 'Account':
        return <AccountScreen navigation={mockNavigation} />;
      case 'Wishlist':
        return <WishlistScreen navigation={mockNavigation} />;
      case 'Notifications':
        return <NotificationsScreen navigation={mockNavigation} />;
      default:
        return <HomeScreen navigation={mockNavigation} />;
    }
  };

  return (
    <NavigationContext.Provider value={navigationValue}>
      {renderScreen()}
    </NavigationContext.Provider>
  );
}

// Main App Navigator (Customer App) - SPA-STYLE NAVIGATION
export default function AppNavigator() {
  // For now, always use the fallback since we're having React Navigation issues in Expo Go
  // This provides a working app while we resolve the navigation dependencies
  console.log('Using fallback navigator for Expo Go compatibility');
  return <FallbackNavigator />;

  // TODO: Re-enable Stack Navigator once dependencies are resolved
  /*
  // If Stack Navigator is not available, show fallback
  if (!Stack || !createStackNavigator) {
    console.warn('Stack Navigator not available, showing fallback');
    return <FallbackNavigator />;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyleInterpolator: () => ({
          cardStyle: {
            opacity: 1,
            transform: [],
          },
        }),
        gestureEnabled: false,
        animation: 'none',
        transitionSpec: {
          open: {
            animation: 'timing',
            config: {
              duration: 0,
            },
          },
          close: {
            animation: 'timing',
            config: {
              duration: 0,
            },
          },
        },
      }}
    >
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="Orders" component={OrdersScreen} />
      <Stack.Screen name="Cart" component={CartScreen} />
      <Stack.Screen name="Search" component={SearchScreen} />
      <Stack.Screen name="Account" component={AccountScreen} />
      <Stack.Screen name="Wishlist" component={WishlistScreen} />
      <Stack.Screen name="Notifications" component={NotificationsScreen} />
    </Stack.Navigator>
  );
  */
}


